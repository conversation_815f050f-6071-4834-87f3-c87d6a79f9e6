<template>
    <div class="document-study-page">
        <van-nav-bar :title="resourceName" left-arrow @click-left="goBack" fixed placeholder />

        <div class="document-container">
            <!-- 文档内容区域 -->
            <div class="document-content">
                <!-- PDF文档 -->
                <div v-if="isPDF" class="pdf-viewer">
                    <iframe
                        :src="documentUrl"
                        class="pdf-frame"
                        frameborder="0"
                        @load="onDocumentLoaded"
                    ></iframe>
                </div>

                <!-- 图片文档 -->
                <div v-else-if="isImage" class="image-viewer">
                    <van-image
                        :src="resourceUrl"
                        fit="contain"
                        class="document-image"
                        @load="onDocumentLoaded"
                        @error="onDocumentError"
                    />
                </div>

                <!-- 文本文档 -->
                <div v-else-if="isText" class="text-viewer">
                    <div class="text-content" v-html="textContent"></div>
                </div>

                <!-- 其他格式文档 -->
                <div v-else class="unsupported-format">
                    <van-empty image="error" description="不支持的文档格式" />
                    <van-button type="primary" @click="downloadDocument" class="download-btn">
                        下载文档
                    </van-button>
                </div>

                <!-- 加载状态 -->
                <div class="loading-overlay" v-show="isLoading">
                    <van-loading size="24px">加载中...</van-loading>
                </div>
            </div>

            <!-- 学习进度信息 -->
            <div class="study-progress">
                <div class="progress-header">
                    <h3>学习进度</h3>
                    <span class="progress-percentage">{{ Math.round(studyProgress) }}%</span>
                </div>
                <van-progress :percentage="studyProgress" color="#2563eb" track-color="#f0f0f0" />
                <div class="progress-details">
                    <div class="detail-item">
                        <span class="label">学习时长:</span>
                        <span class="value">{{ formatTime(studyTime) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">开始时间:</span>
                        <span class="value">{{ formatDateTime(startTime) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">完成状态:</span>
                        <span class="value" :class="{ completed: isCompleted }">
                            {{ isCompleted ? '已完成' : '学习中' }}
                        </span>
                    </div>
                </div>

                <!-- 完成学习按钮 -->
                <van-button
                    v-if="!isCompleted && studyTime >= minStudyTime"
                    type="primary"
                    block
                    @click="completeStudy"
                    class="complete-btn"
                >
                    完成学习
                </van-button>

                <div v-else-if="!isCompleted" class="study-tip">
                    <van-icon name="info-o" />
                    <span>请至少学习 {{ Math.ceil(minStudyTime / 60) }} 分钟后完成</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DocumentStudyPage',
    data() {
        return {
            // 路由参数
            courseId: this.$route.query.courseId,
            lessonId: this.$route.query.lessonId,
            resourceId: this.$route.query.resourceId,
            resourceUrl: this.$route.query.resourceUrl,
            resourceName: this.$route.query.resourceName,

            // 文档状态
            isLoading: true,
            textContent: '',
            documentUrl: '',

            // 学习进度
            studyProgress: 0,
            studyTime: 0,
            startTime: Date.now(),
            isCompleted: false,
            minStudyTime: 300, // 最少学习5分钟

            // 定时器
            studyTimer: null,
            saveProgressTimer: null
        }
    },
    computed: {
        // 判断文档类型
        isPDF() {
            return (
                this.resourceUrl &&
                (this.resourceUrl.toLowerCase().includes('.pdf') ||
                    this.resourceUrl.toLowerCase().includes('pdf'))
            )
        },

        isImage() {
            const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
            return (
                this.resourceUrl &&
                imageExts.some(ext => this.resourceUrl.toLowerCase().includes(ext))
            )
        },

        isText() {
            const textExts = ['.txt', '.md', '.html', '.htm']
            return (
                this.resourceUrl &&
                textExts.some(ext => this.resourceUrl.toLowerCase().includes(ext))
            )
        }
    },
    mounted() {
        this.initDocumentViewer()
        this.loadStudyProgress()
        this.startStudyTimer()
        this.setupAutoSave()
    },
    beforeDestroy() {
        this.clearTimers()
        this.saveStudyProgress()
    },
    methods: {
        // 初始化文档查看器
        initDocumentViewer() {
            if (this.isPDF) {
                // 使用浏览器内置PDF查看器
                this.documentUrl = this.resourceUrl
            } else if (this.isText) {
                this.loadTextContent()
            } else {
                this.isLoading = false
            }
        },

        // 加载文本内容
        async loadTextContent() {
            try {
                const response = await fetch(this.resourceUrl)
                this.textContent = await response.text()

                // 如果是Markdown，可以进行转换
                if (this.resourceUrl.toLowerCase().includes('.md')) {
                    // 这里可以使用markdown解析库
                    this.textContent = this.textContent.replace(/\n/g, '<br>')
                }

                this.isLoading = false
            } catch (error) {
                console.error('加载文档内容失败:', error)
                this.onDocumentError()
            }
        },

        // 文档加载完成
        onDocumentLoaded() {
            this.isLoading = false
        },

        // 文档加载错误
        onDocumentError() {
            this.isLoading = false
            this.$toast.fail('文档加载失败')
        },

        // 下载文档
        downloadDocument() {
            window.open(this.resourceUrl, '_blank')
        },

        // 开始学习计时
        startStudyTimer() {
            this.studyTimer = setInterval(() => {
                this.studyTime += 1
                this.updateStudyProgress()
            }, 1000)
        },

        // 更新学习进度
        updateStudyProgress() {
            // 基于学习时长计算进度
            this.studyProgress = Math.min((this.studyTime / this.minStudyTime) * 100, 100)
        },

        // 完成学习
        completeStudy() {
            this.isCompleted = true
            this.studyProgress = 100
            this.saveStudyProgress()
            this.$toast.success('恭喜！文档学习完成')
        },

        // 加载学习进度
        loadStudyProgress() {
            try {
                const progressKey = `document_progress_${this.courseId}_${this.lessonId}_${this.resourceId}`
                const savedProgress = localStorage.getItem(progressKey)

                if (savedProgress) {
                    const progress = JSON.parse(savedProgress)
                    this.studyTime = progress.studyTime || 0
                    this.studyProgress = progress.studyProgress || 0
                    this.isCompleted = progress.isCompleted || false
                    this.startTime = progress.startTime || Date.now()
                }
            } catch (error) {
                console.error('加载学习进度失败:', error)
            }
        },

        // 保存学习进度
        saveStudyProgress() {
            const progressData = {
                courseId: this.courseId,
                lessonId: this.lessonId,
                resourceId: this.resourceId,
                studyTime: this.studyTime,
                studyProgress: this.studyProgress,
                isCompleted: this.isCompleted,
                startTime: this.startTime,
                lastUpdateTime: Date.now()
            }

            const progressKey = `document_progress_${this.courseId}_${this.lessonId}_${this.resourceId}`
            localStorage.setItem(progressKey, JSON.stringify(progressData))
        },

        // 设置自动保存
        setupAutoSave() {
            this.saveProgressTimer = setInterval(() => {
                this.saveStudyProgress()
            }, 30000) // 每30秒保存一次
        },

        // 清理定时器
        clearTimers() {
            clearInterval(this.studyTimer)
            clearInterval(this.saveProgressTimer)
        },

        // 格式化时间
        formatTime(seconds) {
            const hours = Math.floor(seconds / 3600)
            const minutes = Math.floor((seconds % 3600) / 60)
            const secs = seconds % 60

            if (hours > 0) {
                return `${hours}小时${minutes}分钟${secs}秒`
            } else if (minutes > 0) {
                return `${minutes}分钟${secs}秒`
            } else {
                return `${secs}秒`
            }
        },

        // 格式化日期时间
        formatDateTime(timestamp) {
            const date = new Date(timestamp)
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            })
        },

        // 返回上一页
        goBack() {
            this.saveStudyProgress()
            this.$router.go(-1)
        }
    }
}
</script>

<style lang="scss" scoped>
// 主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.document-study-page {
    background: $background-blue;
    min-height: 100vh;

    .document-container {
        padding: 20px 16px;

        .document-content {
            background: $white;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);
            position: relative;
            min-height: 400px;

            .pdf-viewer {
                width: 100%;
                height: 500px;

                .pdf-frame {
                    width: 100%;
                    height: 100%;
                    border: none;
                }
            }

            .image-viewer {
                padding: 20px;
                text-align: center;

                .document-image {
                    max-width: 100%;
                    max-height: 500px;
                    border-radius: 8px;
                }
            }

            .text-viewer {
                padding: 24px;

                .text-content {
                    font-size: 16px;
                    line-height: 1.8;
                    color: $text-primary;
                    word-wrap: break-word;
                    white-space: pre-wrap;

                    // 文本样式优化
                    h1,
                    h2,
                    h3,
                    h4,
                    h5,
                    h6 {
                        color: $text-primary;
                        margin: 20px 0 12px 0;
                        font-weight: 600;
                    }

                    p {
                        margin: 12px 0;
                    }

                    code {
                        background: $background-blue;
                        padding: 2px 6px;
                        border-radius: 4px;
                        font-family: 'Courier New', monospace;
                        font-size: 14px;
                    }

                    pre {
                        background: $background-blue;
                        padding: 16px;
                        border-radius: 8px;
                        overflow-x: auto;
                        margin: 16px 0;

                        code {
                            background: none;
                            padding: 0;
                        }
                    }
                }
            }

            .unsupported-format {
                padding: 40px 20px;
                text-align: center;

                .download-btn {
                    margin-top: 20px;
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    border: none;
                    border-radius: 12px;
                    height: 44px;
                    font-weight: 600;
                }
            }

            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .study-progress {
            background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);

            .progress-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;

                h3 {
                    font-size: 18px;
                    font-weight: 600;
                    color: $text-primary;
                    margin: 0;
                }

                .progress-percentage {
                    font-size: 20px;
                    font-weight: 700;
                    color: $primary-blue;
                }
            }

            .progress-details {
                margin-top: 16px;
                display: flex;
                flex-direction: column;
                gap: 8px;

                .detail-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .label {
                        font-size: 14px;
                        color: $text-secondary;
                        font-weight: 500;
                    }

                    .value {
                        font-size: 14px;
                        color: $text-primary;
                        font-weight: 600;

                        &.completed {
                            color: #10b981;
                        }
                    }
                }
            }

            .complete-btn {
                margin-top: 20px;
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border: none;
                border-radius: 12px;
                height: 44px;
                font-weight: 600;
            }

            .study-tip {
                margin-top: 16px;
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px 16px;
                background: rgba(245, 158, 11, 0.1);
                border-radius: 8px;
                color: #f59e0b;
                font-size: 14px;

                .van-icon {
                    font-size: 16px;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 375px) {
    .document-study-page .document-container {
        padding: 16px 12px;

        .document-content {
            .pdf-viewer {
                height: 400px;
            }

            .text-viewer {
                padding: 16px;

                .text-content {
                    font-size: 15px;
                    line-height: 1.7;
                }
            }
        }

        .study-progress {
            padding: 16px;

            .progress-header h3 {
                font-size: 16px;
            }

            .progress-percentage {
                font-size: 18px;
            }
        }
    }
}
</style>
