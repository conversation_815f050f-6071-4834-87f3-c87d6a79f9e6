<template>
    <div class="my-enrollments-page">
        <van-nav-bar title="我的报名" fixed placeholder />

        <div class="content">
            <!-- 状态筛选 -->
            <div class="status-filter">
                <van-tabs v-model="activeStatus" @change="onStatusChange">
                    <van-tab
                        v-for="status in statusList"
                        :key="status.value"
                        :title="status.label"
                    />
                </van-tabs>
            </div>

            <!-- 报名列表 -->
            <div class="enrollments-list">
                <van-list
                    v-model="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    @load="onLoad"
                >
                    <div
                        v-for="enrollment in enrollmentList"
                        :key="enrollment.id"
                        class="enrollment-item"
                        @click="goToCourseDetail(enrollment.courseId)"
                    >
                        <div class="enrollment-card">
                            <div class="course-info">
                                <van-image
                                    :src="enrollment.courseCover"
                                    fit="cover"
                                    class="course-cover"
                                />
                                <div class="course-details">
                                    <div class="course-title">{{ enrollment.courseTitle }}</div>
                                    <div class="course-teacher">{{ enrollment.teacher }}</div>
                                    <div class="enrollment-time">
                                        报名时间：{{ formatTime(enrollment.enrollTime) }}
                                    </div>
                                </div>
                            </div>

                            <div class="enrollment-meta">
                                <div class="price">{{ getPriceText(enrollment.price) }}</div>
                                <div class="status-badge">
                                    <van-tag :type="getStatusType(enrollment.status)">
                                        {{ getStatusText(enrollment.status) }}
                                    </van-tag>
                                </div>
                            </div>

                            <!-- 学习进度 -->
                            <div
                                v-if="
                                    enrollment.status === 'studying' ||
                                    enrollment.status === 'completed'
                                "
                                class="study-progress"
                            >
                                <div class="progress-info">
                                    <span class="progress-label">学习进度</span>
                                    <span class="progress-text">{{ enrollment.progress }}%</span>
                                </div>
                                <van-progress
                                    :percentage="enrollment.progress"
                                    :color="
                                        enrollment.status === 'completed' ? '#52c41a' : '#2563eb'
                                    "
                                    stroke-width="6"
                                />
                                <div
                                    v-if="enrollment.status === 'completed' && enrollment.score"
                                    class="score-info"
                                >
                                    <span class="score-label">课程成绩：</span>
                                    <span class="score-value">{{ enrollment.score }}分</span>
                                </div>
                            </div>

                            <div class="enrollment-actions">
                                <van-button
                                    v-if="enrollment.status === 'studying'"
                                    size="small"
                                    type="primary"
                                    @click.stop="continueStudy()"
                                >
                                    继续学习
                                </van-button>
                                <van-button
                                    v-if="enrollment.status === 'pending'"
                                    size="small"
                                    @click.stop="cancelEnrollment()"
                                >
                                    取消报名
                                </van-button>
                                <van-button
                                    v-if="enrollment.status === 'completed'"
                                    size="small"
                                    type="default"
                                    @click.stop="reviewCourse()"
                                >
                                    复习课程
                                </van-button>
                                <van-button
                                    v-if="enrollment.status === 'completed'"
                                    size="small"
                                    type="primary"
                                    @click.stop="downloadCertificate()"
                                >
                                    下载证书
                                </van-button>
                            </div>

                            <!-- 审核备注 -->
                            <div v-if="enrollment.remark" class="enrollment-remark">
                                <div class="remark-label">审核备注：</div>
                                <div class="remark-content">{{ enrollment.remark }}</div>
                            </div>
                        </div>
                    </div>
                </van-list>
            </div>

            <!-- 空状态 -->
            <van-empty
                v-if="!loading && enrollmentList.length === 0"
                description="暂无报名记录"
                image="search"
            >
                <van-button type="primary" @click="goToCourses"> 去报名课程 </van-button>
            </van-empty>
        </div>
    </div>
</template>

<script>
export default {
    name: 'MyEnrollmentsPage',
    data() {
        return {
            activeStatus: 0,
            loading: false,
            finished: false,
            statusList: [
                { value: 'all', label: '全部' },
                { value: 'pending', label: '待审核' },
                { value: 'studying', label: '学习中' },
                { value: 'completed', label: '已结课' }
            ],
            enrollmentList: []
        }
    },
    created() {
        this.loadEnrollments()
    },
    methods: {
        // 加载报名记录
        async loadEnrollments() {
            try {
                this.loading = true

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 1000))

                // 模拟报名数据
                const mockData = [
                    {
                        id: 1,
                        courseId: 1,
                        courseTitle: '人体解剖学基础',
                        courseCover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                        teacher: '张教授',
                        price: 0,
                        status: 'pending',
                        enrollTime: '2024-01-15T10:30:00Z',
                        remark: '',
                        progress: 0
                    },
                    {
                        id: 2,
                        courseId: 2,
                        courseTitle: '内科学精讲',
                        courseCover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                        teacher: '李主任',
                        price: 199,
                        status: 'studying',
                        enrollTime: '2024-01-10T14:20:00Z',
                        remark: '审核通过，请按时学习',
                        progress: 65,
                        studyStartTime: '2024-01-12T09:00:00Z'
                    },
                    {
                        id: 3,
                        courseId: 3,
                        courseTitle: '护理学基础',
                        courseCover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                        teacher: '王护士长',
                        price: 0,
                        status: 'completed',
                        enrollTime: '2024-01-08T09:15:00Z',
                        remark: '课程已完成，成绩优秀',
                        progress: 100,
                        studyStartTime: '2024-01-09T10:00:00Z',
                        completedTime: '2024-01-20T16:30:00Z',
                        score: 95
                    },
                    {
                        id: 4,
                        courseId: 4,
                        courseTitle: '药理学进阶',
                        courseCover: 'https://img.yzcdn.cn/vant/cat.jpeg',
                        teacher: '赵博士',
                        price: 299,
                        status: 'studying',
                        enrollTime: '2024-01-05T11:20:00Z',
                        remark: '学习进度良好',
                        progress: 30,
                        studyStartTime: '2024-01-06T14:00:00Z'
                    }
                ]

                // 根据状态筛选
                const currentStatus = this.statusList[this.activeStatus].value
                if (currentStatus === 'all') {
                    this.enrollmentList = mockData
                } else {
                    this.enrollmentList = mockData.filter(item => item.status === currentStatus)
                }

                this.finished = true
            } catch (error) {
                this.$toast.fail('加载失败，请重试')
            } finally {
                this.loading = false
            }
        },

        // 状态切换
        onStatusChange() {
            this.enrollmentList = []
            this.finished = false
            this.loadEnrollments()
        },

        // 加载更多
        onLoad() {
            this.loadEnrollments()
        },

        // 跳转到课程详情
        goToCourseDetail(courseId) {
            this.$router.push(`/course-detail/${courseId}`)
        },

        // 跳转到课程列表
        goToCourses() {
            this.$router.push('/courses')
        },

        // 格式化时间
        formatTime(timeString) {
            const date = new Date(timeString)
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            })
        },

        // 获取价格文本
        getPriceText(price) {
            return price === 0 ? '免费' : `¥${price}`
        },

        // 获取状态类型
        getStatusType(status) {
            const typeMap = {
                pending: 'warning',
                studying: 'primary',
                completed: 'success'
            }
            return typeMap[status] || 'default'
        },

        // 获取状态文本
        getStatusText(status) {
            const textMap = {
                pending: '待审核',
                studying: '学习中',
                completed: '已结课'
            }
            return textMap[status] || '未知'
        },

        // 继续学习
        continueStudy() {
            this.$toast('跳转到学习页面')
            // 可以跳转到具体的学习页面
            // this.$router.push(`/course-study/${enrollment.courseId}`)
        },

        // 复习课程
        reviewCourse() {
            this.$toast('跳转到复习页面')
            // this.$router.push(`/course-review/${enrollment.courseId}`)
        },

        // 下载证书
        downloadCertificate() {
            this.$toast('开始下载证书')
            // 实际项目中这里会调用下载证书的API
        },

        // 取消报名
        async cancelEnrollment() {
            try {
                await this.$dialog.confirm({
                    title: '确认取消',
                    message: '确定要取消报名吗？'
                })

                // 模拟API调用
                await new Promise(resolve => setTimeout(resolve, 500))

                this.$toast.success('取消成功')
                this.loadEnrollments()
            } catch (error) {
                if (error !== 'cancel') {
                    this.$toast.fail('取消失败，请重试')
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.my-enrollments-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 0;

        // 状态筛选
        .status-filter {
            background: $white;
            margin-bottom: 12px;

            ::v-deep .van-tabs {
                .van-tabs__wrap {
                    background: $white;
                    padding: 0 16px;
                }

                .van-tab {
                    color: $text-secondary;
                    font-weight: 600;

                    &--active {
                        color: $primary-blue;
                    }
                }

                .van-tabs__line {
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    height: 3px;
                    border-radius: 2px;
                }
            }
        }

        // 报名列表
        .enrollments-list {
            padding: 0 16px;

            .enrollment-item {
                margin-bottom: 16px;

                .enrollment-card {
                    background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                    border-radius: 16px;
                    padding: 16px;
                    box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                    border: 1px solid rgba(37, 99, 235, 0.05);
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.98);
                        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15);
                    }

                    .course-info {
                        display: flex;
                        gap: 12px;
                        margin-bottom: 12px;

                        .course-cover {
                            width: 80px;
                            height: 60px;
                            border-radius: 8px;
                            overflow: hidden;
                            flex-shrink: 0;

                            ::v-deep .van-image {
                                width: 100%;
                                height: 100%;
                            }
                        }

                        .course-details {
                            flex: 1;

                            .course-title {
                                font-size: 16px;
                                font-weight: 700;
                                color: $text-primary;
                                margin-bottom: 4px;
                                line-height: 1.3;
                            }

                            .course-teacher {
                                font-size: 14px;
                                color: $text-secondary;
                                margin-bottom: 4px;
                            }

                            .enrollment-time {
                                font-size: 12px;
                                color: $text-light;
                            }
                        }
                    }

                    .enrollment-meta {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 12px;

                        .price {
                            font-size: 18px;
                            font-weight: 700;
                            color: $primary-blue;
                        }

                        .status-badge {
                            ::v-deep .van-tag {
                                font-weight: 600;
                                border-radius: 8px;
                            }
                        }
                    }

                    .study-progress {
                        background: rgba(37, 99, 235, 0.05);
                        border-radius: 12px;
                        padding: 16px;
                        margin-bottom: 12px;
                        border: 1px solid rgba(37, 99, 235, 0.1);

                        .progress-info {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 8px;

                            .progress-label {
                                font-size: 14px;
                                font-weight: 600;
                                color: $text-secondary;
                            }

                            .progress-text {
                                font-size: 16px;
                                font-weight: 700;
                                color: $primary-blue;
                            }
                        }

                        ::v-deep .van-progress {
                            margin-bottom: 8px;

                            .van-progress__portion {
                                border-radius: 3px;
                            }
                        }

                        .score-info {
                            display: flex;
                            align-items: center;
                            gap: 4px;
                            margin-top: 8px;

                            .score-label {
                                font-size: 12px;
                                color: $text-secondary;
                            }

                            .score-value {
                                font-size: 14px;
                                font-weight: 700;
                                color: #52c41a;
                            }
                        }
                    }

                    .enrollment-actions {
                        display: flex;
                        gap: 8px;
                        margin-bottom: 12px;

                        .van-button {
                            flex: 1;
                            border-radius: 8px;
                            font-weight: 600;

                            &--primary {
                                background: linear-gradient(
                                    135deg,
                                    $primary-blue 0%,
                                    $light-blue 100%
                                );
                                border: none;
                                box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
                            }

                            &--default {
                                border: 1px solid rgba(37, 99, 235, 0.2);
                                color: $primary-blue;
                                background: $white;
                            }
                        }
                    }

                    .enrollment-remark {
                        background: rgba(37, 99, 235, 0.05);
                        border-radius: 8px;
                        padding: 12px;
                        border-left: 4px solid $primary-blue;

                        .remark-label {
                            font-size: 12px;
                            font-weight: 600;
                            color: $primary-blue;
                            margin-bottom: 4px;
                        }

                        .remark-content {
                            font-size: 14px;
                            color: $text-secondary;
                            line-height: 1.4;
                        }
                    }
                }
            }
        }

        // 空状态
        ::v-deep .van-empty {
            padding: 60px 20px;

            .van-empty__description {
                color: $text-secondary;
                margin-bottom: 20px;
            }

            .van-button {
                background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                border: none;
                border-radius: 12px;
                font-weight: 600;
                box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);
            }
        }
    }
}

// 响应式设计
@media (max-width: 480px) {
    .my-enrollments-page {
        .content {
            .enrollments-list {
                padding: 0 12px;

                .enrollment-item {
                    .enrollment-card {
                        padding: 12px;

                        .course-info {
                            .course-cover {
                                width: 70px;
                                height: 50px;
                            }

                            .course-details {
                                .course-title {
                                    font-size: 15px;
                                }
                            }
                        }

                        .enrollment-meta {
                            .price {
                                font-size: 16px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
