<template>
    <div class="courses-page">
        <van-nav-bar title="课程中心" fixed placeholder />

        <div class="content">
            <!-- 搜索栏 -->
            <div class="search-section">
                <van-search
                    v-model="searchValue"
                    placeholder="搜索课程"
                    @search="onSearch"
                    @clear="onClear"
                />
            </div>

            <!-- 分类标签 -->
            <div class="category-section">
                <van-tabs v-model="activeCategory" @change="onCategoryChange" :ellipsis="false">
                    <van-tab
                        v-for="category in categories"
                        :key="category.ID"
                        :title="category.CategoryName"
                    />
                </van-tabs>
            </div>

            <!-- 课程列表（不使用 van-list） -->
            <div class="course-list">
                <div class="course-item">
                    <div
                        v-for="course in courseList"
                        :key="course.ID"
                        class="course-card"
                        @click="goToCourseDetail(course)"
                    >
                        <div class="course-image">
                            <van-image :src="course.CoverImage || course.ListImage" fit="cover" />
                            <div class="course-tags">
                                <van-tag v-if="course.IsHot" type="warning" size="mini"
                                    >热门</van-tag
                                >
                                <van-tag :type="getLevelColor(course.Level)" size="mini">{{
                                    getLevelText(course.Level)
                                }}</van-tag>
                            </div>
                            <div class="course-duration">
                                <van-icon name="clock-o" />
                                {{ formatDuration(course.TotalDuration) }}
                            </div>
                        </div>
                        <div class="course-info">
                            <div class="course-title">{{ course.CourseName }}</div>
                            <div class="course-desc">{{ course.CourseDesc }}</div>
                            <div class="course-teacher" v-if="course.DocentName">
                                <van-icon name="manager-o" />
                                {{ course.DocentName }}
                            </div>
                            <div class="course-meta">
                                <div class="course-lessons">
                                    <van-icon name="play-circle-o" />
                                    {{ course.TotalLessons }}个课时
                                </div>
                                <div class="course-requirements" v-if="course.FinishRequirements">
                                    <van-icon name="certificate" />
                                    {{ course.FinishRequirements }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 触底触发器 -->
                <div ref="loadMoreTrigger" class="load-more-trigger"></div>
                <!-- 底部提示区 -->
                <div class="list-footer">
                    <div v-if="loading" class="loading-box">
                        <van-loading type="spinner" color="#2563eb" size="20px" />
                        <span class="loading-text">加载中...</span>
                    </div>
                    <div v-else-if="finished && courseList.length > 0" class="finished-box">
                        <span class="line"></span>
                        <span class="text">没有更多了</span>
                        <span class="line"></span>
                    </div>
                </div>

                <van-empty
                    v-if="!loading && courseList.length === 0"
                    description="暂无课程"
                    image="search"
                />
            </div>
        </div>
    </div>
</template>

<script>
import { getCourseCategories, getCourseList } from '@/api/course'

export default {
    name: 'CoursesPage',
    data() {
        return {
            searchValue: '',
            activeCategory: 0,
            loading: false,
            finished: false,
            categories: [],
            courseList: [],
            selectedCategory: { ID: 0, CategoryName: '全部', CategoryID: 'ALL' },
            observer: null,
            // 分页参数
            currentPage: 1, // PageIndex，从1开始
            pageSize: 5, // PageSize
            totalCount: 0,
            hasError: false
        }
    },
    created() {
        this.loadCourseCategories()
        this.loadCourseList()
    },
    mounted() {
        this.initIntersectionObserver()
    },
    // keep-alive 组件激活时调用
    activated() {
        console.log('课程中心页面被激活')
        // 重新初始化滚动监听器
        this.$nextTick(() => {
            this.initIntersectionObserver()
        })
    },
    // keep-alive 组件停用时调用
    deactivated() {
        console.log('课程中心页面被停用')
        // 销毁滚动监听器以避免内存泄漏
        this.destroyIntersectionObserver()
    },
    beforeDestroy() {
        this.destroyIntersectionObserver()
    },
    methods: {
        formatDuration(seconds) {
            if (!seconds) return '0分钟'
            const hours = Math.floor(seconds / 3600)
            const minutes = Math.floor((seconds % 3600) / 60)

            if (hours > 0) {
                return `${hours}小时${minutes > 0 ? minutes + '分钟' : ''}`
            }
            return `${minutes}分钟`
        },
        getLevelText(level) {
            const levelMap = {
                1: '初级',
                2: '中级',
                3: '高级',
                4: '专家级'
            }
            return levelMap[level] || '初级'
        },
        getLevelColor(level) {
            const colorMap = {
                1: 'success', // 绿色
                2: 'primary', // 蓝色
                3: 'danger', // 红色
                4: 'warning' // 橙色（专家级）
            }
            return colorMap[level] || 'success'
        },
        // 建立滚动触底观察器（IntersectionObserver）
        initIntersectionObserver() {
            if (this.observer) {
                this.destroyIntersectionObserver()
            }
            const el = this.$refs.loadMoreTrigger
            if (!el) return
            this.observer = new IntersectionObserver(entries => {
                const entry = entries[0]
                if (entry && entry.isIntersecting) {
                    if (!this.loading && !this.finished) {
                        this.fetchMore()
                    }
                }
            })
            this.observer.observe(el)
        },
        destroyIntersectionObserver() {
            if (this.observer) {
                this.observer.disconnect()
                this.observer = null
            }
        },
        // 获取课程分类
        async loadCourseCategories() {
            try {
                const res = await getCourseCategories()
                console.log('课程分类:', res)

                if (res && res.Data && res.Data.PageList) {
                    // 在分类列表前添加"全部"选项
                    this.categories = [
                        { ID: 0, CategoryName: '全部', CategoryID: 'ALL' },
                        ...res.Data.PageList
                    ]
                } else if (res && res.Data) {
                    // 如果直接返回数组
                    const categoryList = Array.isArray(res.Data) ? res.Data : []
                    this.categories = [
                        { ID: 0, CategoryName: '全部', CategoryID: 'ALL' },
                        ...categoryList
                    ]
                }
            } catch (error) {
                console.error('获取课程分类失败:', error)
            }
        },
        // 获取课程列表（重置并加载第一页）
        async loadCourseList() {
            try {
                // 重置分页状态
                this.currentPage = 1
                this.finished = false
                this.loading = false
                this.courseList = []

                // 拉取第一页
                await this.fetchMore()
                // 组件挂载后可能还未建立 observer，这里尝试重建一次
                this.$nextTick(() => this.initIntersectionObserver())
            } catch (error) {
                console.error('获取课程列表失败:', error)
            }
        },
        // 实际拉取数据的方法（加载更多）
        async fetchMore() {
            try {
                if (this.loading || this.finished) return
                this.loading = true
                this.hasError = false

                const res = await getCourseList({
                    PageIndex: this.currentPage,
                    PageSize: this.pageSize,
                    CourseName: this.searchValue,
                    CategoryID:
                        this.selectedCategory.CategoryID === 'ALL'
                            ? null
                            : this.selectedCategory.CategoryID
                })

                const newList =
                    res && res.Data && res.Data.PageList
                        ? res.Data.PageList
                        : res && Array.isArray(res.Data)
                        ? res.Data
                        : []

                if (Array.isArray(newList) && newList.length > 0) {
                    this.courseList = this.courseList.concat(newList)
                }

                if (!newList || newList.length < this.pageSize) {
                    this.finished = true
                } else {
                    this.currentPage += 1
                }
            } catch (error) {
                console.error('获取课程列表失败:', error)
                this.hasError = true
            } finally {
                this.loading = false
            }
        },

        onSearch(value) {
            console.log('搜索:', value)
            // 实现搜索逻辑
            this.loadCourseList()
        },
        onClear() {
            this.searchValue = ''
            // 清空搜索结果
        },
        onCategoryChange(index) {
            this.selectedCategory = this.categories[index]
            console.log('分类切换:', this.selectedCategory.CategoryName)

            // 根据分类筛选课程
            if (this.selectedCategory.CategoryID === 'ALL') {
                // 显示全部课程
                this.loadCourseList()
            } else {
                // 根据分类ID筛选课程
                this.loadCourseList()
            }
        },

        goToCourseDetail(course) {
            console.log('进入课程详情:')
            // 跳转到课程详情页，添加来源参数以便正确返回
            this.$router.push({
                path: `/course-detail/${course.CourseID}`,
                query: { from: 'courses' }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.courses-page {
    background: #f5f6f7;
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 20px 16px;

        .search-section {
            margin-bottom: 20px;

            ::v-deep .van-search {
                .van-search__content {
                    border: 1px solid #2563eb !important;
                    border-radius: 10px !important;
                }

                input {
                    color: $text-primary;
                    font-weight: 500;

                    &::placeholder {
                        color: $text-secondary;
                    }
                }
            }
        }

        .category-section {
            margin-bottom: 24px;
            background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);
            overflow: hidden;

            ::v-deep .van-tabs {
                .van-tabs__wrap {
                    background: transparent;
                }

                .van-tabs__nav {
                    background: transparent;
                }

                .van-tab {
                    color: $text-secondary;
                    font-weight: 600;

                    &--active {
                        color: $primary-blue;
                    }
                }

                .van-tabs__line {
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    height: 3px;
                    border-radius: 2px;
                }
            }
        }

        .course-list {
            .course-item {
                display: grid;
                grid-template-columns: repeat(1, 1fr);
                gap: 16px;

                .course-card {
                    background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 6px 30px rgba(37, 99, 235, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08),
                        0 1px 3px rgba(0, 0, 0, 0.06);
                    border: 1px solid rgba(37, 99, 235, 0.05);
                    transition: all 0.3s ease;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 12px 40px rgba(37, 99, 235, 0.18),
                            0 4px 12px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
                    }

                    &:active {
                        transform: scale(0.96);
                        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15),
                            0 3px 10px rgba(0, 0, 0, 0.1);
                    }

                    .course-image {
                        position: relative;
                        height: 200px;

                        ::v-deep .van-image {
                            width: 100%;
                            height: 100%;
                        }

                        .course-tags {
                            position: absolute;
                            top: 12px;
                            left: 12px;
                            display: flex;
                            gap: 8px;

                            ::v-deep .van-tag {
                                font-weight: 600;
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                                backdrop-filter: blur(4px);
                            }
                        }

                        .course-duration {
                            position: absolute;
                            bottom: 12px;
                            right: 12px;
                            background: rgba(0, 0, 0, 0.7);
                            color: white;
                            padding: 6px 10px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 4px;
                            backdrop-filter: blur(4px);

                            .van-icon {
                                font-size: 12px;
                            }
                        }
                    }

                    .course-info {
                        padding: 18px;

                        .course-title {
                            font-size: 16px;
                            font-weight: 700;
                            color: $text-primary;
                            margin-bottom: 8px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            line-height: 1.4;
                        }

                        .course-desc {
                            font-size: 13px;
                            color: $text-secondary;
                            margin-bottom: 12px;
                            line-height: 1.5;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .course-teacher {
                            font-size: 13px;
                            color: $primary-blue;
                            margin-bottom: 12px;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 6px;

                            .van-icon {
                                font-size: 14px;
                            }
                        }

                        .course-meta {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            gap: 12px;

                            .course-lessons {
                                font-size: 12px;
                                color: $text-secondary;
                                font-weight: 600;
                                display: flex;
                                align-items: center;
                                gap: 4px;
                                background: rgba(37, 99, 235, 0.08);
                                padding: 4px 8px;
                                border-radius: 8px;

                                .van-icon {
                                    font-size: 12px;
                                    color: $primary-blue;
                                }
                            }

                            .course-requirements {
                                font-size: 11px;
                                color: $text-light;
                                font-weight: 500;
                                display: flex;
                                align-items: center;
                                gap: 4px;

                                .van-icon {
                                    font-size: 11px;
                                    color: #10b981;
                                }
                            }
                        }
                    }
                }
            }
            // 底部加载与完成提示样式
            .list-footer {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 16px 0 28px;
                color: $text-secondary;

                .loading-box {
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;

                    .loading-text {
                        font-size: 13px;
                        color: $text-secondary;
                        font-weight: 600;
                    }
                }

                .finished-box {
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    color: $text-light;

                    .line {
                        width: 48px;
                        height: 1px;
                        background: linear-gradient(
                            90deg,
                            transparent,
                            rgba(37, 99, 235, 0.35),
                            transparent
                        );
                    }
                    .text {
                        font-size: 12px;
                        color: $text-secondary;
                        letter-spacing: 1px;
                    }
                }
            }

            // 隐形的底部触发器，留出可见空间
            .load-more-trigger {
                width: 100%;
                height: 1px;
            }
        }
    }
}
</style>
