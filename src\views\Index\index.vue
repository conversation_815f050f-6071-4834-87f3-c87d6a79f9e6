<template>
    <div class="index-view">
        <keep-alive>
            <router-view v-if="$route.meta.keepAlive"></router-view>
        </keep-alive>
        <router-view v-if="!$route.meta.keepAlive"></router-view>
        <app-footer></app-footer>
    </div>
</template>

<script>
// eslint-disable-next-line prettier/prettier
import AppFooter from '@/components/common/AppFooter.vue';
export default {
    name: 'IndexView',
    components: { AppFooter }
}
</script>
<style scoped>
.index-view {
    padding: 10px;
    background-color: #f5f6f7;
}
</style>
