<template>
    <div class="audio-study-page">
        <van-nav-bar :title="resourceName" left-arrow @click-left="goBack" fixed placeholder />

        <div class="audio-container">
            <!-- 音频播放器 -->
            <div class="audio-player-wrapper">
                <div class="audio-cover">
                    <van-image :src="coverImage" fit="cover" class="cover-image" />
                    <div class="play-overlay">
                        <van-icon
                            :name="isPlaying ? 'pause' : 'play'"
                            @click="togglePlay"
                            class="play-btn"
                        />
                    </div>
                </div>

                <audio
                    ref="audioPlayer"
                    :src="resourceUrl"
                    @loadedmetadata="onAudioLoaded"
                    @timeupdate="onTimeUpdate"
                    @ended="onAudioEnded"
                    @play="onAudioPlay"
                    @pause="onAudioPause"
                    @error="onAudioError"
                    preload="metadata"
                >
                    您的浏览器不支持音频播放
                </audio>

                <!-- 音频信息 -->
                <div class="audio-info">
                    <h3 class="audio-title">{{ resourceName }}</h3>
                    <div class="audio-meta">
                        <span class="duration">{{ formatTime(duration) }}</span>
                    </div>
                </div>

                <!-- 进度控制 -->
                <div class="progress-container">
                    <div class="progress-bar" @click="seekTo">
                        <div class="progress-played" :style="{ width: playProgress + '%' }"></div>
                        <div class="progress-thumb" :style="{ left: playProgress + '%' }"></div>
                    </div>
                    <div class="time-info">
                        <span class="current-time">{{ formatTime(currentTime) }}</span>
                        <span class="total-time">{{ formatTime(duration) }}</span>
                    </div>
                </div>

                <!-- 控制按钮 -->
                <div class="control-buttons">
                    <van-icon name="replay" @click="replay" class="control-btn" />
                    <van-icon name="minus" @click="seekBackward" class="control-btn" />
                    <van-icon
                        :name="isPlaying ? 'pause' : 'play'"
                        @click="togglePlay"
                        class="main-play-btn"
                    />
                    <van-icon name="plus" @click="seekForward" class="control-btn" />
                    <div class="speed-control" @click="toggleSpeed">
                        <span>{{ playbackRate }}x</span>
                    </div>
                </div>
            </div>

            <!-- 学习进度信息 -->
            <div class="study-progress">
                <div class="progress-header">
                    <h3>学习进度</h3>
                    <span class="progress-percentage">{{ Math.round(studyProgress) }}%</span>
                </div>
                <van-progress :percentage="studyProgress" color="#2563eb" track-color="#f0f0f0" />
                <div class="progress-details">
                    <div class="detail-item">
                        <span class="label">已听时长:</span>
                        <span class="value">{{ formatTime(listenedTime) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">总时长:</span>
                        <span class="value">{{ formatTime(duration) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">完成状态:</span>
                        <span class="value" :class="{ completed: isCompleted }">
                            {{ isCompleted ? '已完成' : '学习中' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'AudioStudyPage',
    data() {
        return {
            // 路由参数
            courseId: this.$route.query.courseId,
            lessonId: this.$route.query.lessonId,
            resourceId: this.$route.query.resourceId,
            resourceUrl: this.$route.query.resourceUrl,
            resourceName: this.$route.query.resourceName,
            duration: parseInt(this.$route.query.duration) || 0,

            // 音频播放状态
            isPlaying: false,
            currentTime: 0,
            playProgress: 0,
            playbackRate: 1,
            speedOptions: [0.5, 0.75, 1, 1.25, 1.5, 2],

            // 学习进度
            studyProgress: 0,
            listenedTime: 0,
            isCompleted: false,
            lastSaveTime: 0,

            // 封面图片
            coverImage: 'https://img.yzcdn.cn/vant/cat.jpeg',

            // 定时器
            saveProgressTimer: null
        }
    },
    mounted() {
        this.loadStudyProgress()
        this.setupAutoSave()
    },
    beforeDestroy() {
        this.clearTimers()
        this.saveStudyProgress()
    },
    methods: {
        // 音频加载完成
        onAudioLoaded() {
            // 如果有断点续播位置，跳转到该位置
            if (this.currentTime > 0) {
                this.$refs.audioPlayer.currentTime = this.currentTime
            }
        },

        // 时间更新
        onTimeUpdate() {
            const audio = this.$refs.audioPlayer
            this.currentTime = audio.currentTime
            this.playProgress = (this.currentTime / this.duration) * 100

            // 更新学习进度
            this.updateStudyProgress()
        },

        // 更新学习进度
        updateStudyProgress() {
            const timeDiff = this.currentTime - this.lastSaveTime
            if (timeDiff > 0 && timeDiff < 2) {
                this.listenedTime += timeDiff
            }
            this.lastSaveTime = this.currentTime

            this.studyProgress = Math.min((this.listenedTime / this.duration) * 100, 100)

            if (this.studyProgress >= 80 && !this.isCompleted) {
                this.isCompleted = true
                this.$toast.success('恭喜！音频学习完成')
            }
        },

        // 播放/暂停切换
        togglePlay() {
            const audio = this.$refs.audioPlayer
            if (this.isPlaying) {
                audio.pause()
            } else {
                audio.play()
            }
        },

        // 音频播放
        onAudioPlay() {
            this.isPlaying = true
        },

        // 音频暂停
        onAudioPause() {
            this.isPlaying = false
        },

        // 音频结束
        onAudioEnded() {
            this.isPlaying = false
            this.isCompleted = true
            this.studyProgress = 100
            this.saveStudyProgress()
            this.$toast.success('音频学习完成！')
        },

        // 音频错误
        onAudioError() {
            this.$toast.fail('音频加载失败，请检查网络连接')
        },

        // 进度条点击跳转
        seekTo(event) {
            const progressBar = event.currentTarget
            const rect = progressBar.getBoundingClientRect()
            const clickX = event.clientX - rect.left
            const percentage = clickX / rect.width
            const targetTime = percentage * this.duration

            this.$refs.audioPlayer.currentTime = targetTime
        },

        // 快退15秒
        seekBackward() {
            const audio = this.$refs.audioPlayer
            audio.currentTime = Math.max(0, audio.currentTime - 15)
        },

        // 快进15秒
        seekForward() {
            const audio = this.$refs.audioPlayer
            audio.currentTime = Math.min(this.duration, audio.currentTime + 15)
        },

        // 重播
        replay() {
            const audio = this.$refs.audioPlayer
            audio.currentTime = 0
            audio.play()
        },

        // 切换播放速度
        toggleSpeed() {
            const currentIndex = this.speedOptions.indexOf(this.playbackRate)
            const nextIndex = (currentIndex + 1) % this.speedOptions.length
            this.playbackRate = this.speedOptions[nextIndex]
            this.$refs.audioPlayer.playbackRate = this.playbackRate
        },

        // 加载学习进度
        loadStudyProgress() {
            try {
                const progressKey = `audio_progress_${this.courseId}_${this.lessonId}_${this.resourceId}`
                const savedProgress = localStorage.getItem(progressKey)

                if (savedProgress) {
                    const progress = JSON.parse(savedProgress)
                    this.currentTime = progress.currentTime || 0
                    this.listenedTime = progress.listenedTime || 0
                    this.studyProgress = progress.studyProgress || 0
                    this.isCompleted = progress.isCompleted || false
                }
            } catch (error) {
                console.error('加载学习进度失败:', error)
            }
        },

        // 保存学习进度
        saveStudyProgress() {
            const progressData = {
                courseId: this.courseId,
                lessonId: this.lessonId,
                resourceId: this.resourceId,
                currentTime: this.currentTime,
                listenedTime: this.listenedTime,
                studyProgress: this.studyProgress,
                isCompleted: this.isCompleted,
                lastUpdateTime: Date.now()
            }

            const progressKey = `audio_progress_${this.courseId}_${this.lessonId}_${this.resourceId}`
            localStorage.setItem(progressKey, JSON.stringify(progressData))
        },

        // 设置自动保存
        setupAutoSave() {
            this.saveProgressTimer = setInterval(() => {
                this.saveStudyProgress()
            }, 30000)
        },

        // 清理定时器
        clearTimers() {
            clearInterval(this.saveProgressTimer)
        },

        // 格式化时间
        formatTime(seconds) {
            if (!seconds || isNaN(seconds)) return '00:00'

            const minutes = Math.floor(seconds / 60)
            const secs = Math.floor(seconds % 60)
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
        },

        // 返回上一页
        goBack() {
            this.saveStudyProgress()
            this.$router.go(-1)
        }
    }
}
</script>

<style lang="scss" scoped>
// 主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.audio-study-page {
    background: $background-blue;
    min-height: 100vh;

    .audio-container {
        padding: 20px 16px;

        .audio-player-wrapper {
            background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);

            .audio-cover {
                position: relative;
                width: 200px;
                height: 200px;
                margin: 0 auto 24px;
                border-radius: 50%;
                overflow: hidden;

                .cover-image {
                    width: 100%;
                    height: 100%;
                }

                .play-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.3);
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .play-btn {
                        color: white;
                        font-size: 48px;
                        cursor: pointer;
                    }
                }
            }

            .audio-info {
                text-align: center;
                margin-bottom: 24px;

                .audio-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: $text-primary;
                    margin: 0 0 8px 0;
                }

                .audio-meta {
                    .duration {
                        font-size: 14px;
                        color: $text-secondary;
                    }
                }
            }

            .progress-container {
                margin-bottom: 24px;

                .progress-bar {
                    position: relative;
                    height: 4px;
                    background: rgba(37, 99, 235, 0.1);
                    border-radius: 2px;
                    cursor: pointer;
                    margin-bottom: 8px;

                    .progress-played {
                        height: 100%;
                        background: $primary-blue;
                        border-radius: 2px;
                        transition: width 0.1s ease;
                    }

                    .progress-thumb {
                        position: absolute;
                        top: -4px;
                        width: 12px;
                        height: 12px;
                        background: $primary-blue;
                        border-radius: 50%;
                        transform: translateX(-50%);
                        transition: left 0.1s ease;
                    }
                }

                .time-info {
                    display: flex;
                    justify-content: space-between;
                    font-size: 12px;
                    color: $text-secondary;
                }
            }

            .control-buttons {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 24px;

                .control-btn {
                    color: $text-secondary;
                    font-size: 24px;
                    cursor: pointer;

                    &:active {
                        color: $primary-blue;
                    }
                }

                .main-play-btn {
                    color: $primary-blue;
                    font-size: 36px;
                    cursor: pointer;
                }

                .speed-control {
                    padding: 6px 12px;
                    background: rgba(37, 99, 235, 0.1);
                    border-radius: 16px;
                    font-size: 14px;
                    color: $primary-blue;
                    cursor: pointer;
                    font-weight: 600;
                }
            }
        }

        .study-progress {
            background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);

            .progress-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;

                h3 {
                    font-size: 18px;
                    font-weight: 600;
                    color: $text-primary;
                    margin: 0;
                }

                .progress-percentage {
                    font-size: 20px;
                    font-weight: 700;
                    color: $primary-blue;
                }
            }

            .progress-details {
                margin-top: 16px;
                display: flex;
                flex-direction: column;
                gap: 8px;

                .detail-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .label {
                        font-size: 14px;
                        color: $text-secondary;
                        font-weight: 500;
                    }

                    .value {
                        font-size: 14px;
                        color: $text-primary;
                        font-weight: 600;

                        &.completed {
                            color: #10b981;
                        }
                    }
                }
            }
        }
    }
}
</style>
