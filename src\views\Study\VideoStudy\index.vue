<template>
    <div class="video-study-page">
        <van-nav-bar :title="resourceName" left-arrow @click-left="goBack" fixed placeholder />

        <div class="video-container">
            <!-- 视频播放器 -->
            <div class="video-player-wrapper">
                <video
                    ref="videoPlayer"
                    :src="resourceUrl"
                    class="video-player"
                    @loadedmetadata="onVideoLoaded"
                    @timeupdate="onTimeUpdate"
                    @ended="onVideoEnded"
                    @play="onVideoPlay"
                    @pause="onVideoPause"
                    @error="onVideoError"
                    preload="metadata"
                    webkit-playsinline
                    playsinline
                    x5-video-player-type="h5"
                    x5-video-player-fullscreen="true"
                >
                    您的浏览器不支持视频播放
                </video>

                <!-- 自定义控制栏 -->
                <div class="video-controls" v-show="showControls">
                    <div class="progress-container">
                        <div class="progress-bar" @click="seekTo">
                            <div
                                class="progress-buffer"
                                :style="{ width: bufferProgress + '%' }"
                            ></div>
                            <div
                                class="progress-played"
                                :style="{ width: playProgress + '%' }"
                            ></div>
                            <div class="progress-thumb" :style="{ left: playProgress + '%' }"></div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <div class="left-controls">
                            <van-icon
                                :name="isPlaying ? 'pause' : 'play'"
                                @click="togglePlay"
                                class="play-btn"
                            />
                            <span class="time-display">
                                {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
                            </span>
                        </div>

                        <div class="right-controls">
                            <div class="speed-control" @click="showSpeedMenu = !showSpeedMenu">
                                <span>{{ playbackRate }}x</span>
                                <van-icon name="arrow-up" />
                            </div>
                            <van-icon
                                name="enlarge"
                                @click="toggleFullscreen"
                                class="fullscreen-btn"
                            />
                        </div>
                    </div>
                </div>

                <!-- 倍速选择菜单 -->
                <div class="speed-menu" v-show="showSpeedMenu">
                    <div
                        v-for="speed in speedOptions"
                        :key="speed"
                        class="speed-option"
                        :class="{ active: playbackRate === speed }"
                        @click="setPlaybackRate(speed)"
                    >
                        {{ speed }}x
                    </div>
                </div>

                <!-- 加载状态 -->
                <div class="loading-overlay" v-show="isLoading">
                    <van-loading size="24px" color="#fff">加载中...</van-loading>
                </div>
            </div>

            <!-- 学习进度信息 -->
            <div class="study-progress">
                <div class="progress-header">
                    <h3>学习进度</h3>
                    <span class="progress-percentage">{{ Math.round(studyProgress) }}%</span>
                </div>
                <van-progress :percentage="studyProgress" color="#2563eb" track-color="#f0f0f0" />
                <div class="progress-details">
                    <div class="detail-item">
                        <span class="label">已观看时长:</span>
                        <span class="value">{{ formatTime(watchedTime) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">总时长:</span>
                        <span class="value">{{ formatTime(duration) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">完成状态:</span>
                        <span class="value" :class="{ completed: isCompleted }">
                            {{ isCompleted ? '已完成' : '学习中' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 验证码弹窗 -->
        <van-dialog
            v-model="showVerifyDialog"
            title="学习验证"
            :show-cancel-button="false"
            :close-on-click-overlay="false"
            :close-on-popstate="false"
            class="verify-dialog"
        >
            <div class="verify-content">
                <p class="verify-tip">请在 {{ verifyCountdown }} 秒内输入验证码继续学习</p>
                <div class="verify-code-display">{{ verifyCode }}</div>
                <van-field
                    v-model="userVerifyInput"
                    placeholder="请输入验证码"
                    center
                    clearable
                    @keyup.enter="submitVerify"
                />
                <van-button
                    type="primary"
                    block
                    @click="submitVerify"
                    :disabled="!userVerifyInput"
                    class="verify-submit-btn"
                >
                    确认
                </van-button>
            </div>
        </van-dialog>
    </div>
</template>

<script>
export default {
    name: 'VideoStudyPage',
    data() {
        return {
            // 路由参数
            courseId: this.$route.query.courseId,
            lessonId: this.$route.query.lessonId,
            resourceId: this.$route.query.resourceId,
            resourceUrl: this.$route.query.resourceUrl,
            resourceName: this.$route.query.resourceName,
            duration: parseInt(this.$route.query.duration) || 0,

            // 视频播放状态
            isPlaying: false,
            isLoading: true,
            currentTime: 0,
            playProgress: 0,
            bufferProgress: 0,
            showControls: true,
            controlsTimer: null,

            // 播放控制
            playbackRate: 1,
            speedOptions: [0.5, 0.75, 1, 1.25, 1.5, 2],
            showSpeedMenu: false,

            // 学习进度
            studyProgress: 0,
            watchedTime: 0,
            isCompleted: false,
            lastSaveTime: 0,

            // 验证码系统
            showVerifyDialog: false,
            verifyCode: '',
            userVerifyInput: '',
            verifyCountdown: 30,
            verifyTimer: null,
            verifyInterval: null,
            lastVerifyTime: 0,

            // 断点续播
            resumePosition: 0,
            saveProgressTimer: null
        }
    },
    mounted() {
        this.initVideoPlayer()
        this.loadStudyProgress()
        this.setupVerifySystem()
        this.setupAutoSave()
    },
    beforeDestroy() {
        this.clearTimers()
        this.saveStudyProgress()
    },
    methods: {
        // 初始化视频播放器
        initVideoPlayer() {
            const video = this.$refs.videoPlayer
            if (!video) return

            // 设置视频事件监听
            video.addEventListener('click', this.togglePlay)
            video.addEventListener('dblclick', this.toggleFullscreen)

            // 隐藏控制栏定时器
            this.setupControlsTimer()
        },

        // 加载学习进度
        async loadStudyProgress() {
            try {
                // 从本地存储或服务器获取学习进度
                const progressKey = `study_progress_${this.courseId}_${this.lessonId}_${this.resourceId}`
                const savedProgress = localStorage.getItem(progressKey)

                if (savedProgress) {
                    const progress = JSON.parse(savedProgress)
                    this.resumePosition = progress.currentTime || 0
                    this.watchedTime = progress.watchedTime || 0
                    this.studyProgress = progress.studyProgress || 0
                    this.isCompleted = progress.isCompleted || false
                }

                // 这里可以调用API获取服务器端的学习进度
                // const res = await getStudyProgress({
                //     courseId: this.courseId,
                //     lessonId: this.lessonId,
                //     resourceId: this.resourceId
                // })
            } catch (error) {
                console.error('加载学习进度失败:', error)
            }
        },

        // 保存学习进度
        saveStudyProgress() {
            const progressData = {
                courseId: this.courseId,
                lessonId: this.lessonId,
                resourceId: this.resourceId,
                currentTime: this.currentTime,
                watchedTime: this.watchedTime,
                studyProgress: this.studyProgress,
                isCompleted: this.isCompleted,
                lastUpdateTime: Date.now()
            }

            // 保存到本地存储
            const progressKey = `study_progress_${this.courseId}_${this.lessonId}_${this.resourceId}`
            localStorage.setItem(progressKey, JSON.stringify(progressData))

            // 这里可以调用API保存到服务器
            // saveStudyProgress(progressData)
        },

        // 视频加载完成
        onVideoLoaded() {
            this.isLoading = false
            const video = this.$refs.videoPlayer

            // 如果有断点续播位置，跳转到该位置
            if (this.resumePosition > 0) {
                video.currentTime = this.resumePosition
                this.$toast(`已恢复到上次学习位置: ${this.formatTime(this.resumePosition)}`)
            }
        },

        // 时间更新
        onTimeUpdate() {
            const video = this.$refs.videoPlayer
            this.currentTime = video.currentTime
            this.playProgress = (this.currentTime / this.duration) * 100

            // 更新缓冲进度
            if (video.buffered.length > 0) {
                this.bufferProgress = (video.buffered.end(0) / this.duration) * 100
            }

            // 更新学习进度
            this.updateStudyProgress()

            // 检查是否需要弹出验证码
            this.checkVerifyTrigger()
        },

        // 更新学习进度
        updateStudyProgress() {
            // 计算观看时长（避免快进刷时长）
            const timeDiff = this.currentTime - this.lastSaveTime
            if (timeDiff > 0 && timeDiff < 2) {
                // 正常播放速度范围内
                this.watchedTime += timeDiff
            }
            this.lastSaveTime = this.currentTime

            // 计算学习进度百分比
            this.studyProgress = Math.min((this.watchedTime / this.duration) * 100, 100)

            // 判断是否完成（观看80%以上视为完成）
            if (this.studyProgress >= 80 && !this.isCompleted) {
                this.isCompleted = true
                this.$toast.success('恭喜！课程学习完成')
            }
        },

        // 播放/暂停切换
        togglePlay() {
            const video = this.$refs.videoPlayer
            if (this.isPlaying) {
                video.pause()
            } else {
                video.play()
            }
        },

        // 视频播放
        onVideoPlay() {
            this.isPlaying = true
        },

        // 视频暂停
        onVideoPause() {
            this.isPlaying = false
        },

        // 视频结束
        onVideoEnded() {
            this.isPlaying = false
            this.isCompleted = true
            this.studyProgress = 100
            this.saveStudyProgress()
            this.$toast.success('课程学习完成！')
        },

        // 视频错误
        onVideoError() {
            this.isLoading = false
            this.$toast.fail('视频加载失败，请检查网络连接')
        },

        // 进度条点击跳转
        seekTo(event) {
            const progressBar = event.currentTarget
            const rect = progressBar.getBoundingClientRect()
            const clickX = event.clientX - rect.left
            const percentage = clickX / rect.width
            const targetTime = percentage * this.duration

            this.$refs.videoPlayer.currentTime = targetTime
        },

        // 设置播放倍速
        setPlaybackRate(rate) {
            this.playbackRate = rate
            this.$refs.videoPlayer.playbackRate = rate
            this.showSpeedMenu = false
        },

        // 全屏切换
        toggleFullscreen() {
            const video = this.$refs.videoPlayer
            if (video.requestFullscreen) {
                video.requestFullscreen()
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen()
            } else if (video.mozRequestFullScreen) {
                video.mozRequestFullScreen()
            }
        },

        // 设置验证码系统
setupVerifySystem() {
    // 3秒后弹出验证码
    this.verifyTimer = setTimeout(() => {
        this.triggerVerify()
    }, 3 * 1000) // 3秒
},

        // 触发验证码
        triggerVerify() {
            if (!this.isPlaying) return

            // 暂停视频
            this.$refs.videoPlayer.pause()

            // 生成4位随机验证码
            this.verifyCode = Math.floor(1000 + Math.random() * 9000).toString()
            this.userVerifyInput = ''
            this.verifyCountdown = 30
            this.showVerifyDialog = true

            // 开始倒计时
            this.verifyInterval = setInterval(() => {
                this.verifyCountdown--
                if (this.verifyCountdown <= 0) {
                    this.verifyTimeout()
                }
            }, 1000)
        },

        // 验证码超时
        verifyTimeout() {
            this.showVerifyDialog = false
            this.clearInterval(this.verifyInterval)
            this.$toast.fail('验证超时，已记录异常行为')

            // 记录异常行为
            this.recordAbnormalBehavior('verify_timeout')

            // 设置下一次验证
            this.setupNextVerify()
        },

        // 提交验证码
        submitVerify() {
            if (this.userVerifyInput === this.verifyCode) {
                this.showVerifyDialog = false
                this.clearInterval(this.verifyInterval)
                this.$toast.success('验证成功')

                // 继续播放视频
                this.$refs.videoPlayer.play()

                // 设置下一次验证
                this.setupNextVerify()
            } else {
                this.$toast.fail('验证码错误，请重新输入')
                this.userVerifyInput = ''
            }
        },

        // 设置下一次验证
        setupNextVerify() {
            const randomTime = Math.random() * (15 - 5) + 5 // 5-15分钟
            this.verifyTimer = setTimeout(() => {
                this.triggerVerify()
            }, randomTime * 60 * 1000)
        },

        // 检查验证触发条件
        checkVerifyTrigger() {
            // 可以根据其他条件触发验证，比如检测到异常行为
        },

        // 记录异常行为
        recordAbnormalBehavior(type) {
            const abnormalData = {
                courseId: this.courseId,
                lessonId: this.lessonId,
                resourceId: this.resourceId,
                type: type,
                timestamp: Date.now(),
                currentTime: this.currentTime
            }

            // 保存到本地
            const abnormalKey = `abnormal_behavior_${this.courseId}_${this.lessonId}`
            const existingData = JSON.parse(localStorage.getItem(abnormalKey) || '[]')
            existingData.push(abnormalData)
            localStorage.setItem(abnormalKey, JSON.stringify(existingData))

            // 这里可以调用API上报异常行为
            // reportAbnormalBehavior(abnormalData)
        },

        // 设置控制栏定时器
        setupControlsTimer() {
            this.showControls = true
            clearTimeout(this.controlsTimer)
            this.controlsTimer = setTimeout(() => {
                if (this.isPlaying) {
                    this.showControls = false
                }
            }, 3000)
        },

        // 设置自动保存
        setupAutoSave() {
            this.saveProgressTimer = setInterval(() => {
                this.saveStudyProgress()
            }, 30000) // 每30秒保存一次
        },

        // 清理定时器
        clearTimers() {
            clearTimeout(this.controlsTimer)
            clearTimeout(this.verifyTimer)
            clearInterval(this.verifyInterval)
            clearInterval(this.saveProgressTimer)
        },

        // 格式化时间
        formatTime(seconds) {
            if (!seconds || isNaN(seconds)) return '00:00'

            const hours = Math.floor(seconds / 3600)
            const minutes = Math.floor((seconds % 3600) / 60)
            const secs = Math.floor(seconds % 60)

            if (hours > 0) {
                return `${hours.toString().padStart(2, '0')}:${minutes
                    .toString()
                    .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
            } else {
                return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
            }
        },

        // 返回上一页
        goBack() {
            this.saveStudyProgress()
            this.$router.go(-1)
        }
    }
}
</script>

<style lang="scss" scoped>
// 主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.video-study-page {
    background: $background-blue;
    min-height: 100vh;

    .video-container {
        padding: 20px 16px;

        .video-player-wrapper {
            position: relative;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 24px;

            .video-player {
                width: 100%;
                height: 300px;
                object-fit: contain;
                background: #000;
            }

            .video-controls {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
                padding: 20px 16px 16px;
                transition: opacity 0.3s ease;

                .progress-container {
                    margin-bottom: 12px;

                    .progress-bar {
                        position: relative;
                        height: 4px;
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 2px;
                        cursor: pointer;

                        .progress-buffer {
                            position: absolute;
                            height: 100%;
                            background: rgba(255, 255, 255, 0.5);
                            border-radius: 2px;
                            transition: width 0.3s ease;
                        }

                        .progress-played {
                            position: absolute;
                            height: 100%;
                            background: $primary-blue;
                            border-radius: 2px;
                            transition: width 0.1s ease;
                        }

                        .progress-thumb {
                            position: absolute;
                            top: -4px;
                            width: 12px;
                            height: 12px;
                            background: $primary-blue;
                            border-radius: 50%;
                            transform: translateX(-50%);
                            transition: left 0.1s ease;
                        }
                    }
                }

                .control-buttons {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .left-controls {
                        display: flex;
                        align-items: center;
                        gap: 12px;

                        .play-btn {
                            color: white;
                            font-size: 24px;
                            cursor: pointer;
                        }

                        .time-display {
                            color: white;
                            font-size: 14px;
                            font-weight: 500;
                        }
                    }

                    .right-controls {
                        display: flex;
                        align-items: center;
                        gap: 16px;

                        .speed-control {
                            display: flex;
                            align-items: center;
                            gap: 4px;
                            color: white;
                            font-size: 14px;
                            cursor: pointer;
                            padding: 4px 8px;
                            border-radius: 4px;
                            background: rgba(255, 255, 255, 0.1);

                            .van-icon {
                                font-size: 12px;
                            }
                        }

                        .fullscreen-btn {
                            color: white;
                            font-size: 20px;
                            cursor: pointer;
                        }
                    }
                }
            }

            .speed-menu {
                position: absolute;
                bottom: 60px;
                right: 16px;
                background: rgba(0, 0, 0, 0.8);
                border-radius: 8px;
                padding: 8px 0;
                min-width: 80px;

                .speed-option {
                    padding: 8px 16px;
                    color: white;
                    font-size: 14px;
                    cursor: pointer;
                    text-align: center;

                    &:hover {
                        background: rgba(255, 255, 255, 0.1);
                    }

                    &.active {
                        background: $primary-blue;
                        color: white;
                    }
                }
            }

            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .study-progress {
            background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);

            .progress-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;

                h3 {
                    font-size: 18px;
                    font-weight: 600;
                    color: $text-primary;
                    margin: 0;
                }

                .progress-percentage {
                    font-size: 20px;
                    font-weight: 700;
                    color: $primary-blue;
                }
            }

            .progress-details {
                margin-top: 16px;
                display: flex;
                flex-direction: column;
                gap: 8px;

                .detail-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .label {
                        font-size: 14px;
                        color: $text-secondary;
                        font-weight: 500;
                    }

                    .value {
                        font-size: 14px;
                        color: $text-primary;
                        font-weight: 600;

                        &.completed {
                            color: #10b981;
                        }
                    }
                }
            }
        }
    }
}

// 验证码弹窗样式
::v-deep .verify-dialog {
    .van-dialog__content {
        padding: 24px;

        .verify-content {
            text-align: center;

            .verify-tip {
                font-size: 14px;
                color: $text-secondary;
                margin-bottom: 16px;
            }

            .verify-code-display {
                font-size: 32px;
                font-weight: 700;
                color: $primary-blue;
                background: $background-blue;
                padding: 16px;
                border-radius: 12px;
                margin-bottom: 16px;
                letter-spacing: 8px;
                font-family: 'Courier New', monospace;
            }

            .van-field {
                margin-bottom: 16px;

                ::v-deep .van-field__control {
                    text-align: center;
                    font-size: 18px;
                    font-weight: 600;
                }
            }

            .verify-submit-btn {
                background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                border: none;
                border-radius: 12px;
                height: 44px;
                font-weight: 600;
            }
        }
    }
}

// 响应式设计
@media (max-width: 375px) {
    .video-study-page .video-container {
        padding: 16px 12px;

        .video-player-wrapper .video-player {
            height: 180px;
        }

        .study-progress {
            padding: 16px;

            .progress-header h3 {
                font-size: 16px;
            }

            .progress-percentage {
                font-size: 18px;
            }
        }
    }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
    .video-study-page {
        .video-container .video-player-wrapper .video-player {
            height: 60vh;
        }
    }
}
</style>
