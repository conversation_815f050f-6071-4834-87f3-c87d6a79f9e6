<template>
    <div class="study-page">
        <van-nav-bar title="我的学习" fixed placeholder />

        <div class="content">
            <!-- 学习统计 -->
            <div class="study-stats">
                <van-grid :column-num="3" :border="false">
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.totalCourses }}</div>
                            <div class="stat-label">报名课程</div>
                        </div>
                    </van-grid-item>
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.totalHours }}</div>
                            <div class="stat-label">学习时长(小时)</div>
                        </div>
                    </van-grid-item>
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.certificates }}</div>
                            <div class="stat-label">已完成课程</div>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 课程状态筛选 -->
            <div class="status-section">
                <van-tabs v-model="activeStatus" @change="onStatusChange" :ellipsis="false">
                    <van-tab
                        v-for="status in statusList"
                        :key="status.value"
                        :title="status.label"
                    />
                </van-tabs>
            </div>

            <!-- 我的课程列表 -->
            <div class="my-course-list">
                <div class="course-item">
                    <div
                        v-for="course in courseList"
                        :key="course.ID"
                        class="course-card"
                        @click="goToCourseDetail(course)"
                    >
                        <div class="course-image">
                            <van-image :src="course.CoverImage" fit="cover" />
                            <div class="course-tags">
                                <van-tag v-if="course.IsHot" type="warning" size="mini"
                                    >热门</van-tag
                                >
                                <van-tag :type="getLevelColor(course.Level)" size="mini">{{
                                    getLevelText(course.Level)
                                }}</van-tag>
                                <van-tag
                                    :type="getStatusColor(course.ApprovalStatus)"
                                    size="mini"
                                    >{{ getStatusText(course.ApprovalStatus) }}</van-tag
                                >
                            </div>
                        </div>
                        <div class="course-info">
                            <div class="course-title">{{ course.CourseName }}</div>
                            <div class="course-desc">{{ course.CourseDesc }}</div>
                            <div class="course-teacher">
                                <van-icon name="user-o" />
                                {{ course.InstructorName }}
                            </div>
                            <div v-if="course.RecordState !== 0" class="course-meta">
                                <van-progress
                                    :percentage="course.ProgressPercentage || 0"
                                    stroke-width="6"
                                    color="#2563eb"
                                />
                                <span class="progress-text"
                                    >{{ course.ProgressPercentage || 0 }}%</span
                                >
                            </div>
                            <div v-if="course.RecordState !== 0" class="course-meta">
                                <div class="course-time">
                                    <van-icon name="clock-o" />
                                    报名时间: {{ formatTime(course.EnrollTime) }}
                                </div>
                                <div class="course-expire">
                                    <van-icon name="calendar-o" />
                                    到期时间: {{ formatTime(course.ExpireTime) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 触底触发器 -->
                <div ref="loadMoreTrigger" class="load-more-trigger"></div>
                <!-- 底部提示区 -->
                <div class="list-footer">
                    <div v-if="loading" class="loading-box">
                        <van-loading type="spinner" color="#2563eb" size="20px" />
                        <span class="loading-text">加载中...</span>
                    </div>
                    <div v-else-if="finished && courseList.length > 0" class="finished-box">
                        <span class="line"></span>
                        <span class="text">没有更多了</span>
                        <span class="line"></span>
                    </div>
                </div>

                <van-empty
                    v-if="!loading && courseList.length === 0"
                    description="暂无课程"
                    image="search"
                />
            </div>
        </div>
    </div>
</template>

<script>
import { getEnrollCourse, getMyCourses } from '@/api/course.js'
export default {
    name: 'StudyPage',
    data() {
        return {
            studyStats: {
                totalCourses: 0,
                totalHours: 0,
                certificates: 0
            },
            activeStatus: 0,
            statusList: [
                { value: 'all', label: '全部' },
                { value: '2', label: '学习中' },
                { value: '3', label: '已结课' },
                { value: '1', label: '待审核' },
                { value: '4', label: '已过期' }
            ],
            courseList: [],
            loading: false,
            finished: false,
            observer: null,
            // 分页参数
            currentPage: 1,
            pageSize: 5,
            totalCount: 0,
            hasError: false
        }
    },
    created() {
        this.loadCourseList()
        this.getMyCourses()
    },
    mounted() {
        this.initIntersectionObserver()
    },
    beforeDestroy() {
        this.destroyIntersectionObserver()
    },
    methods: {
        // 获取我的课程统计
        async getMyCourses() {
            try {
                const res = await getMyCourses({
                    StudentId: this.$store.state.user.userInfo.UserID
                })

                if (res && res.Data && res.Data.Summary) {
                    const summary = res.Data.Summary
                    this.studyStats = {
                        totalCourses: summary.CompletedCount + summary.InProgressCount,
                        totalHours: summary.TotalLearningHours || 0,
                        certificates: summary.CompletedCount || 0
                    }
                    console.log('学习统计更新:', this.studyStats)
                }
            } catch (error) {
                console.error('获取学习统计失败:', error)
            }
        },
        // 格式化时间
        formatTime(timeStr) {
            if (!timeStr) return ''
            const date = new Date(timeStr)
            return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
                date.getDate()
            ).padStart(2, '0')}`
        },
        // 获取课程级别文本
        getLevelText(level) {
            const levelMap = {
                1: '初级',
                2: '中级',
                3: '高级',
                4: '专家级'
            }
            return levelMap[level] || '初级'
        },
        // 获取课程级别颜色
        getLevelColor(level) {
            const colorMap = {
                1: 'success', // 绿色
                2: 'primary', // 蓝色
                3: 'danger', // 红色
                4: 'warning' // 橙色（专家级）
            }
            return colorMap[level] || 'success'
        },
        // 获取审核状态文本
        getStatusText(status) {
            const statusMap = {
                0: '待审核',
                1: '已通过',
                2: '已拒绝'
            }
            return statusMap[status] || '待审核'
        },
        // 获取审核状态颜色
        getStatusColor(status) {
            const colorMap = {
                0: 'warning', // 橙色 - 待审核
                1: 'success', // 绿色 - 已通过
                2: 'danger' // 红色 - 已拒绝
            }
            return colorMap[status] || 'warning'
        },
        // 建立滚动触底观察器
        initIntersectionObserver() {
            if (this.observer) {
                this.destroyIntersectionObserver()
            }
            const el = this.$refs.loadMoreTrigger
            if (!el) return
            this.observer = new IntersectionObserver(entries => {
                const entry = entries[0]
                if (entry && entry.isIntersecting) {
                    if (!this.loading && !this.finished) {
                        this.fetchMore()
                    }
                }
            })
            this.observer.observe(el)
        },
        destroyIntersectionObserver() {
            if (this.observer) {
                this.observer.disconnect()
                this.observer = null
            }
        },
        // 状态切换
        onStatusChange(index) {
            const selectedStatus = this.statusList[index]
            console.log('状态切换:', selectedStatus.label, 'RecordState:', selectedStatus.value)
            this.loadCourseList()
        },
        // 获取课程列表（重置并加载第一页）
        async loadCourseList() {
            try {
                // 重置分页状态
                this.currentPage = 1
                this.finished = false
                this.loading = false
                this.courseList = []

                // 拉取第一页
                await this.fetchMore()
                // 组件挂载后可能还未建立 observer，这里尝试重建一次
                this.$nextTick(() => this.initIntersectionObserver())
            } catch (error) {
                console.error('获取课程列表失败:', error)
            }
        },
        // 实际拉取数据的方法（加载更多）
        async fetchMore() {
            try {
                if (this.loading || this.finished) return
                this.loading = true
                this.hasError = false

                // 获取当前选中的状态
                const selectedStatus = this.statusList[this.activeStatus]
                const params = {
                    StudentId: this.$store.state.user.userInfo.UserID,
                    PageIndex: this.currentPage,
                    PageSize: this.pageSize
                }

                // 如果不是"全部"状态，则添加 RecordState 参数
                if (selectedStatus.value !== 'all') {
                    params.RecordState = parseInt(selectedStatus.value)
                }

                const res = await getEnrollCourse(params)

                const newList =
                    res && res.Data && res.Data.PageList
                        ? res.Data.PageList
                        : res && Array.isArray(res.Data)
                        ? res.Data
                        : []

                if (Array.isArray(newList) && newList.length > 0) {
                    this.courseList = this.courseList.concat(newList)
                }

                if (!newList || newList.length < this.pageSize) {
                    this.finished = true
                } else {
                    this.currentPage += 1
                }
            } catch (error) {
                console.error('获取课程列表失败:', error)
                this.hasError = true
            } finally {
                this.loading = false
            }
        },
        goToCourseDetail(course) {
            console.log('进入课程详情:', course.CourseName)
            // 跳转到课程详情页
            this.$router.push(`/course-detail/${course.CourseID}`)
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.study-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 20px 16px;

        .study-stats {
            background: linear-gradient(135deg, $white 0%, #f1f5f9 100%);
            border-radius: 16px;
            margin-bottom: 24px;
            padding: 0px 0;
            overflow: hidden;
            box-shadow: 0 6px 30px rgba(37, 99, 235, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(37, 99, 235, 0.05);
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 12px 40px rgba(37, 99, 235, 0.18), 0 4px 12px rgba(0, 0, 0, 0.12),
                    0 2px 6px rgba(0, 0, 0, 0.08);
            }

            .stat-item {
                text-align: center;

                .stat-number {
                    font-size: 28px;
                    font-weight: 700;
                    color: $primary-blue;
                    margin-bottom: 6px;
                }

                .stat-label {
                    font-size: 13px;
                    color: $text-secondary;
                    font-weight: 600;
                }
            }
        }

        .progress-section {
            margin-bottom: 24px;

            ::v-deep .van-cell {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);
                padding: 20px 16px;

                .van-cell__title {
                    color: $text-primary;
                    font-weight: 600;
                    font-size: 16px;
                }

                .van-cell__value {
                    color: $text-secondary;
                    font-weight: 600;
                }

                .van-progress {
                    margin-top: 8px;

                    .van-progress__portion {
                        background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    }
                }
            }
        }

        .status-section {
            margin-bottom: 24px;
            background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
            border-radius: 16px;
            box-shadow: 0 6px 30px rgba(37, 99, 235, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(37, 99, 235, 0.05);
            overflow: hidden;
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 12px 40px rgba(37, 99, 235, 0.18), 0 4px 12px rgba(0, 0, 0, 0.12),
                    0 2px 6px rgba(0, 0, 0, 0.08);
            }

            ::v-deep .van-tabs {
                .van-tabs__wrap {
                    background: transparent;
                }

                .van-tabs__nav {
                    background: transparent;
                }

                .van-tab {
                    color: $text-secondary;
                    font-weight: 600;

                    &--active {
                        color: $primary-blue;
                    }
                }

                .van-tabs__line {
                    background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    height: 3px;
                    border-radius: 2px;
                }
            }
        }

        .my-course-list {
            .course-item {
                display: grid;
                grid-template-columns: repeat(1, 1fr);
                gap: 16px;

                .course-card {
                    background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 6px 30px rgba(37, 99, 235, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08),
                        0 1px 3px rgba(0, 0, 0, 0.06);
                    border: 1px solid rgba(37, 99, 235, 0.05);
                    transition: all 0.3s ease;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 12px 40px rgba(37, 99, 235, 0.18),
                            0 4px 12px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
                    }

                    &:active {
                        transform: scale(0.96);
                        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15),
                            0 3px 10px rgba(0, 0, 0, 0.1);
                    }

                    .course-image {
                        position: relative;
                        height: 200px;

                        ::v-deep .van-image {
                            width: 100%;
                            height: 100%;
                        }

                        .course-tags {
                            position: absolute;
                            top: 12px;
                            left: 12px;
                            display: flex;
                            gap: 8px;
                            flex-wrap: wrap;

                            ::v-deep .van-tag {
                                font-weight: 600;
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                                backdrop-filter: blur(4px);
                            }
                        }
                    }

                    .course-info {
                        padding: 18px;

                        .course-title {
                            font-size: 16px;
                            font-weight: 700;
                            color: $text-primary;
                            margin-bottom: 8px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            line-height: 1.4;
                        }

                        .course-desc {
                            font-size: 13px;
                            color: $text-secondary;
                            margin-bottom: 12px;
                            line-height: 1.5;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .course-teacher {
                            font-size: 13px;
                            color: $primary-blue;
                            margin-bottom: 12px;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 6px;

                            .van-icon {
                                font-size: 14px;
                            }
                        }

                        .course-progress {
                            margin-bottom: 12px;
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            ::v-deep .van-progress {
                                flex: 1;

                                .van-progress__portion {
                                    background: linear-gradient(
                                        135deg,
                                        $primary-blue 0%,
                                        $light-blue 100%
                                    );
                                }
                            }

                            .progress-text {
                                font-size: 12px;
                                color: $text-secondary;
                                font-weight: 600;
                                min-width: 40px;
                            }
                        }

                        .course-meta {
                            display: flex;
                            justify-content: space-between;
                            gap: 8px;

                            .course-time,
                            .course-expire {
                                font-size: 12px;
                                color: $text-secondary;
                                font-weight: 500;
                                display: flex;
                                align-items: center;
                                gap: 4px;

                                .van-icon {
                                    font-size: 12px;
                                    color: $primary-blue;
                                }
                            }
                        }
                    }
                }
            }

            // 底部加载与完成提示样式
            .list-footer {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 16px 0 28px;
                color: $text-secondary;

                .loading-box {
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;

                    .loading-text {
                        font-size: 13px;
                        color: $text-secondary;
                        font-weight: 600;
                    }
                }

                .finished-box {
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    color: $text-light;

                    .line {
                        width: 48px;
                        height: 1px;
                        background: linear-gradient(
                            90deg,
                            transparent,
                            rgba(37, 99, 235, 0.35),
                            transparent
                        );
                    }
                    .text {
                        font-size: 12px;
                        color: $text-secondary;
                        letter-spacing: 1px;
                    }
                }
            }

            // 隐形的底部触发器，留出可见空间
            .load-more-trigger {
                width: 100%;
                height: 1px;
            }
        }
    }
}
</style>
