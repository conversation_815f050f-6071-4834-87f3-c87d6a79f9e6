<template>
    <div class="profile-page">
        <van-nav-bar title="个人中心" fixed placeholder />

        <div class="content">
            <!-- 用户信息卡片 -->
            <div class="user-info-card">
                <!-- 背景装饰 -->
                <div class="card-decoration">
                    <div class="decoration-circle circle-1"></div>
                    <div class="decoration-circle circle-2"></div>
                    <div class="decoration-circle circle-3"></div>
                </div>

                <div class="user-header">
                    <div class="avatar-container">
                        <van-image
                            round
                            width="80"
                            height="80"
                            :src="userInfo.avatar"
                            @click="editAvatar"
                            class="user-avatar"
                        />
                        <div class="avatar-badge">
                            <van-icon name="camera-o" />
                        </div>
                    </div>
                    <div class="user-details">
                        <div class="username">{{ userInfo.name }}</div>
                        <div class="user-level">
                            <van-icon name="medal-o" />
                            {{ userInfo.level }}
                        </div>
                        <div class="user-id">ID: {{ userInfo.id }}</div>
                    </div>
                    <div class="edit-btn" @click="editProfile">
                        <van-icon name="edit" />
                    </div>
                </div>

                <!-- 学习数据统计 -->
                <div class="user-stats">
                    <div class="stats-grid">
                        <div class="stat-item" v-for="(stat, index) in statsData" :key="index">
                            <div class="stat-icon">
                                <van-icon :name="stat.icon" />
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">{{ stat.value }}</div>
                                <div class="stat-label">{{ stat.label }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷功能区 -->
            <div class="quick-actions">
                <div class="section-title">
                    <van-icon name="apps-o" />
                    <span>快捷功能</span>
                </div>
                <div class="actions-grid">
                    <div
                        v-for="action in quickActions"
                        :key="action.id"
                        class="action-item"
                        @click="handleActionClick(action)"
                    >
                        <div class="action-icon" :style="{ background: action.color }">
                            <van-icon :name="action.icon" />
                        </div>
                        <div class="action-title">{{ action.title }}</div>
                    </div>
                </div>
            </div>

            <!-- 功能菜单 -->
            <div class="menu-section">
                <div class="section-title">
                    <van-icon name="wap-nav" />
                    <span>我的服务</span>
                </div>
                <div class="menu-card">
                    <div
                        v-for="menu in menuList"
                        :key="menu.id"
                        class="menu-item"
                        @click="handleMenuClick(menu)"
                    >
                        <div class="menu-icon">
                            <van-icon :name="menu.icon" />
                        </div>
                        <div class="menu-content">
                            <div class="menu-title">{{ menu.title }}</div>
                            <div class="menu-desc">{{ menu.desc }}</div>
                        </div>
                        <div class="menu-extra">
                            <span class="menu-value">{{ menu.value }}</span>
                            <van-icon name="arrow" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设置菜单 -->
            <div class="settings-section">
                <div class="section-title">
                    <van-icon name="setting-o" />
                    <span>设置中心</span>
                </div>
                <div class="settings-card">
                    <div
                        v-for="setting in settingsList"
                        :key="setting.id"
                        class="setting-item"
                        @click="handleSettingClick(setting)"
                    >
                        <div class="setting-icon">
                            <van-icon :name="setting.icon" />
                        </div>
                        <div class="setting-content">
                            <div class="setting-title">{{ setting.title }}</div>
                            <div class="setting-desc">{{ setting.desc }}</div>
                        </div>
                        <van-icon name="arrow" />
                    </div>
                </div>
            </div>

            <!-- 退出登录 -->
            <div class="logout-section">
                <van-button type="danger" block @click="logout" class="logout-btn">
                    <van-icon name="sign-out" />
                    退出登录
                </van-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ProfilePage',
    data() {
        return {
            userInfo: {
                id: '123456',
                name: '张医生',
                level: '高级学员',
                avatar: 'https://img.yzcdn.cn/vant/cat.jpeg'
            },
            userStats: {
                courses: 12,
                certificates: 3,
                points: 2580,
                days: 45
            },
            // 统计数据配置
            statsData: [
                {
                    icon: 'play-circle-o',
                    value: 12,
                    label: '课程',
                    color: '#3b82f6'
                },
                {
                    icon: 'certificate',
                    value: 3,
                    label: '证书',
                    color: '#10b981'
                },
                {
                    icon: 'gold-coin-o',
                    value: 2580,
                    label: '积分',
                    color: '#f59e0b'
                },
                {
                    icon: 'calendar-o',
                    value: 45,
                    label: '学习天数',
                    color: '#ef4444'
                }
            ],
            // 快捷功能
            quickActions: [
                {
                    id: 1,
                    title: '签到',
                    icon: 'calendar-o',
                    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    path: '/checkin'
                },
                {
                    id: 2,
                    title: '消息',
                    icon: 'chat-o',
                    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    path: '/messages'
                },
                {
                    id: 3,
                    title: '收藏',
                    icon: 'star-o',
                    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                    path: '/favorites'
                },
                {
                    id: 4,
                    title: '分享',
                    icon: 'share-o',
                    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                    path: '/share'
                }
            ],
            menuList: [
                {
                    id: 1,
                    title: '我的课程',
                    desc: '查看已报名的课程',
                    icon: 'play-circle-o',
                    value: '12门',
                    path: '/my-courses'
                },
                {
                    id: 2,
                    title: '我的报名',
                    desc: '管理报名信息',
                    icon: 'orders-o',
                    value: '',
                    path: '/my-enrollments'
                },
                {
                    id: 3,
                    title: '我的证书',
                    desc: '查看获得的证书',
                    icon: 'certificate',
                    value: '3张',
                    path: '/my-certificates'
                },
                {
                    id: 4,
                    title: '学习记录',
                    desc: '查看学习历史',
                    icon: 'records',
                    value: '',
                    path: '/study-records'
                },
                {
                    id: 5,
                    title: '积分商城',
                    desc: '兑换精美礼品',
                    icon: 'gift-o',
                    value: '2580积分',
                    path: '/points-mall'
                }
            ],
            settingsList: [
                {
                    id: 1,
                    title: '账号设置',
                    desc: '修改个人信息',
                    icon: 'manager-o',
                    path: '/account-settings'
                },
                {
                    id: 2,
                    title: '消息通知',
                    desc: '通知提醒设置',
                    icon: 'bell',
                    path: '/notifications'
                },
                {
                    id: 3,
                    title: '隐私设置',
                    desc: '隐私保护设置',
                    icon: 'lock',
                    path: '/privacy-settings'
                },
                {
                    id: 4,
                    title: '帮助中心',
                    desc: '常见问题解答',
                    icon: 'question-o',
                    path: '/help'
                },
                {
                    id: 5,
                    title: '关于我们',
                    desc: '了解更多信息',
                    icon: 'info-o',
                    path: '/about'
                }
            ]
        }
    },
    methods: {
        editAvatar() {
            console.log('编辑头像')
            this.$toast('头像编辑功能开发中...')
            // 实现头像编辑功能
        },
        editProfile() {
            console.log('编辑个人信息')
            this.$router.push('/edit-profile')
        },
        handleActionClick(action) {
            console.log('点击快捷功能:', action.title)
            if (action.title === '签到') {
                this.handleCheckin()
            } else {
                this.$router.push(action.path)
            }
        },
        handleCheckin() {
            this.$toast.loading({
                message: '签到中...',
                forbidClick: true,
                duration: 1000
            })

            setTimeout(() => {
                this.$toast.success('签到成功！获得10积分')
                // 更新积分
                this.statsData[2].value += 10
            }, 1000)
        },
        handleMenuClick(menu) {
            console.log('点击菜单:', menu.title)
            this.$router.push(menu.path)
        },
        handleSettingClick(setting) {
            console.log('点击设置:', setting.title)
            this.$router.push(setting.path)
        },
        logout() {
            this.$dialog
                .confirm({
                    title: '确认退出',
                    message: '确定要退出登录吗？',
                    confirmButtonText: '确认退出',
                    cancelButtonText: '取消',
                    confirmButtonColor: '#ee0a24'
                })
                .then(() => {
                    this.$toast.loading({
                        message: '退出中...',
                        forbidClick: true,
                        duration: 1500
                    })

                    setTimeout(() => {
                        // 清除用户信息
                        localStorage.removeItem('token')
                        localStorage.removeItem('userInfo')
                        // 跳转到登录页
                        this.$router.replace('/')
                        this.$toast.success('已退出登录')
                    }, 1500)
                })
                .catch(() => {
                    // 取消退出
                })
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.profile-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 20px 16px;

        // 用户信息卡片
        .user-info-card {
            background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
            border-radius: 24px;
            padding: 28px 24px;
            margin-bottom: 24px;
            box-shadow: 0 12px 48px rgba(37, 99, 235, 0.2);
            color: white;
            position: relative;
            overflow: hidden;

            // 背景装饰
            .card-decoration {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                pointer-events: none;

                .decoration-circle {
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.1);

                    &.circle-1 {
                        width: 120px;
                        height: 120px;
                        top: -60px;
                        right: -60px;
                    }

                    &.circle-2 {
                        width: 80px;
                        height: 80px;
                        bottom: -40px;
                        left: -40px;
                        background: rgba(255, 255, 255, 0.05);
                    }

                    &.circle-3 {
                        width: 60px;
                        height: 60px;
                        top: 50%;
                        right: 20px;
                        background: rgba(255, 255, 255, 0.08);
                    }
                }
            }

            .user-header {
                display: flex;
                align-items: center;
                margin-bottom: 28px;
                position: relative;
                z-index: 1;

                .avatar-container {
                    position: relative;

                    .user-avatar {
                        border: 4px solid rgba(255, 255, 255, 0.3);
                        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
                        transition: all 0.3s ease;

                        &:active {
                            transform: scale(0.95);
                        }
                    }

                    .avatar-badge {
                        position: absolute;
                        bottom: 0;
                        right: 0;
                        width: 28px;
                        height: 28px;
                        background: rgba(255, 255, 255, 0.9);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                        .van-icon {
                            color: $primary-blue;
                            font-size: 14px;
                        }
                    }
                }

                .user-details {
                    flex: 1;
                    margin-left: 20px;

                    .username {
                        font-size: 22px;
                        font-weight: 700;
                        color: white;
                        margin-bottom: 8px;
                        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    }

                    .user-level {
                        font-size: 14px;
                        color: rgba(255, 255, 255, 0.95);
                        margin-bottom: 6px;
                        font-weight: 600;
                        background: rgba(255, 255, 255, 0.25);
                        padding: 6px 14px;
                        border-radius: 20px;
                        display: inline-flex;
                        align-items: center;
                        backdrop-filter: blur(10px);

                        .van-icon {
                            margin-right: 6px;
                            font-size: 16px;
                        }
                    }

                    .user-id {
                        font-size: 13px;
                        color: rgba(255, 255, 255, 0.8);
                        font-weight: 500;
                    }
                }

                .edit-btn {
                    width: 44px;
                    height: 44px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    backdrop-filter: blur(10px);
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.9);
                        background: rgba(255, 255, 255, 0.3);
                    }

                    .van-icon {
                        color: white;
                        font-size: 20px;
                    }
                }
            }

            .user-stats {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 20px;
                padding: 24px 16px;
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                position: relative;
                z-index: 1;

                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 16px;

                    .stat-item {
                        text-align: center;
                        padding: 8px;

                        .stat-icon {
                            width: 40px;
                            height: 40px;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin: 0 auto 12px;

                            .van-icon {
                                color: white;
                                font-size: 18px;
                            }
                        }

                        .stat-content {
                            .stat-number {
                                font-size: 20px;
                                font-weight: 700;
                                color: white;
                                margin-bottom: 4px;
                                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                            }

                            .stat-label {
                                font-size: 12px;
                                color: rgba(255, 255, 255, 0.9);
                                font-weight: 600;
                            }
                        }
                    }
                }
            }
        }

        // 通用标题样式
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 0 4px;

            .van-icon {
                color: $primary-blue;
                font-size: 18px;
                margin-right: 8px;
            }

            span {
                font-size: 16px;
                font-weight: 600;
                color: $text-primary;
            }
        }

        // 快捷功能区
        .quick-actions {
            margin-bottom: 24px;

            .actions-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 16px;
                padding: 20px;
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 20px;
                box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);

                .action-item {
                    text-align: center;
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.95);
                    }

                    .action-icon {
                        width: 48px;
                        height: 48px;
                        border-radius: 16px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 12px;
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

                        .van-icon {
                            color: white;
                            font-size: 20px;
                        }
                    }

                    .action-title {
                        font-size: 13px;
                        font-weight: 600;
                        color: $text-primary;
                    }
                }
            }
        }

        // 功能菜单样式
        .menu-section {
            margin-bottom: 24px;

            .menu-card {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 20px;
                box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);
                overflow: hidden;

                .menu-item {
                    display: flex;
                    align-items: center;
                    padding: 20px;
                    transition: all 0.3s ease;
                    border-bottom: 1px solid rgba(37, 99, 235, 0.06);

                    &:last-child {
                        border-bottom: none;
                    }

                    &:active {
                        background-color: rgba(37, 99, 235, 0.02);
                        transform: scale(0.98);
                    }

                    .menu-icon {
                        width: 44px;
                        height: 44px;
                        background: linear-gradient(
                            135deg,
                            $lightest-blue 0%,
                            rgba(37, 99, 235, 0.1) 100%
                        );
                        border-radius: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 16px;

                        .van-icon {
                            color: $primary-blue;
                            font-size: 20px;
                        }
                    }

                    .menu-content {
                        flex: 1;

                        .menu-title {
                            font-size: 16px;
                            font-weight: 600;
                            color: $text-primary;
                            margin-bottom: 4px;
                        }

                        .menu-desc {
                            font-size: 13px;
                            color: $text-secondary;
                            font-weight: 500;
                        }
                    }

                    .menu-extra {
                        display: flex;
                        align-items: center;

                        .menu-value {
                            font-size: 13px;
                            color: $primary-blue;
                            font-weight: 600;
                            margin-right: 8px;
                        }

                        .van-icon {
                            color: $text-light;
                            font-size: 16px;
                        }
                    }
                }
            }
        }

        // 设置菜单样式
        .settings-section {
            margin-bottom: 32px;

            .settings-card {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 20px;
                box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);
                overflow: hidden;

                .setting-item {
                    display: flex;
                    align-items: center;
                    padding: 20px;
                    transition: all 0.3s ease;
                    border-bottom: 1px solid rgba(37, 99, 235, 0.06);

                    &:last-child {
                        border-bottom: none;
                    }

                    &:active {
                        background-color: rgba(37, 99, 235, 0.02);
                        transform: scale(0.98);
                    }

                    .setting-icon {
                        width: 40px;
                        height: 40px;
                        background: linear-gradient(
                            135deg,
                            rgba(37, 99, 235, 0.1) 0%,
                            rgba(37, 99, 235, 0.05) 100%
                        );
                        border-radius: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 16px;

                        .van-icon {
                            color: $primary-blue;
                            font-size: 18px;
                        }
                    }

                    .setting-content {
                        flex: 1;

                        .setting-title {
                            font-size: 15px;
                            font-weight: 600;
                            color: $text-primary;
                            margin-bottom: 3px;
                        }

                        .setting-desc {
                            font-size: 12px;
                            color: $text-secondary;
                            font-weight: 500;
                        }
                    }

                    .van-icon {
                        color: $text-light;
                        font-size: 16px;
                    }
                }
            }
        }

        // 退出登录按钮
        .logout-section {
            margin-top: 40px;
            padding-bottom: 20px;

            .logout-btn {
                border-radius: 20px;
                height: 56px;
                font-size: 16px;
                font-weight: 600;
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                border: none;
                box-shadow: 0 6px 24px rgba(239, 68, 68, 0.25);
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;

                .van-icon {
                    margin-right: 8px;
                    font-size: 18px;
                }

                &:active {
                    transform: scale(0.98);
                    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.35);
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 375px) {
    .profile-page .content {
        padding: 16px 12px;

        .user-info-card {
            padding: 24px 20px;

            .user-header {
                .user-details .username {
                    font-size: 20px;
                }
            }

            .user-stats .stats-grid {
                gap: 12px;

                .stat-item .stat-content .stat-number {
                    font-size: 18px;
                }
            }
        }

        .quick-actions .actions-grid {
            padding: 16px;
            gap: 12px;

            .action-item .action-icon {
                width: 44px;
                height: 44px;
            }
        }
    }
}

// 动画效果
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.profile-page .content > * {
    animation: fadeInUp 0.6s ease forwards;
}

.profile-page .content > *:nth-child(1) {
    animation-delay: 0.1s;
}
.profile-page .content > *:nth-child(2) {
    animation-delay: 0.2s;
}
.profile-page .content > *:nth-child(3) {
    animation-delay: 0.3s;
}
.profile-page .content > *:nth-child(4) {
    animation-delay: 0.4s;
}
.profile-page .content > *:nth-child(5) {
    animation-delay: 0.5s;
}
</style>
